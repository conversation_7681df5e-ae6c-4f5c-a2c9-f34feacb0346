const { onCall } = require("firebase-functions/v2/https");
const { onSchedule } = require("firebase-functions/v2/scheduler");
const admin = require("firebase-admin");
const functions = require("firebase-functions");
const nodemailer = require('nodemailer');
const { Timestamp } = admin.firestore;

// Initialize admin if not already initialized
if (!admin.apps.length) {
  console.log("Admin not initialized. Initializing now...");
  admin.initializeApp();
}
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();

var transporter = nodemailer.createTransport({
  host: 'smtp.gmail.com',
  port: 465,
  secure: true,
  auth: {
      user: '<EMAIL>',
      pass: 'fgafcymkspvdyjtk'
  }
});

exports.deleteUser = onCall(async (request) => {
  try {
    const { uid } = request.data;
    if (!uid) {
      return { success: false, msg: "User UID is required." };
    }

    // Delete user from Firebase Authentication
    await admin.auth().deleteUser(uid);

    // Optionally, also delete user data from Firestore (if you store user docs)
    // await admin.firestore().collection('users').doc(uid).delete();

    return { success: true, msg: "User deleted successfully." };
  } catch (error) {
    return { success: false, msg: error.message || "Failed to delete user." };
  }
});

exports.createUser = onCall(async (request) => {
  try {
    const { name, email, role, phoneNo, password, customers, designation, sendEmail } = request.data;

    // Basic server-side validation
    if (!email) {
      return { success: false, msg: "Email is required." };
    }
    if (!name) {
      return { success: false, msg: "Name is required." };
    }
    if (!role) {
      return { success: false, msg: "Role is required." };
    }
    if (!phoneNo) {
      return { success: false, msg: "Phone Number is required." };
    }

    // Check if user already exists by email
    try {
      const existingUser = await admin.auth().getUserByEmail(email);
      if (existingUser) {
        return { success: false, code: "user-already-exists", msg: "User with this email already exists." };
      }
    } catch (err) {
      // If user not found, error is expected, proceed normally
      if (err.code !== "auth/user-not-found") {
        throw err; // Re-throw unexpected errors
      }
    }

    // Create user in Firebase Authentication
    const userRecord = await admin.auth().createUser({
      email,
      password,
    });

    // Save additional user data securely
    await db.collection("Users").doc(userRecord.uid).set({
      role: role,
      name: name.trim(),
      email: email.trim(),
      phoneNo: phoneNo,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      status: null,
      password: password.trim(),
      customers: customers,
      designation: designation,
    });

    // Send welcome email if requested (default to true if not specified)
    let emailResult = { success: true };
    if (sendEmail !== false) {
      emailResult = await sendWelcomeEmailHelper(email.trim(), name.trim(), password);
      if (!emailResult.success) {
        console.warn(`User created but email failed: ${emailResult.error}`);
      }
    }

    return { 
      success: true, 
      uid: userRecord.uid,
      emailSent: emailResult.success,
      emailError: emailResult.error || null
    };
  } catch (error) {
    console.error("Error creating user:", error);
    if (error.code === 'auth/weak-password') {
      return { success: false, msg: "Password is too weak or too short." };
    }
    if (error.code === 'auth/invalid-phone-number') {
      return { success: false, msg: "Phone number is invalid or incorrectly formatted." };
    }
    if (error.code === 'auth/invalid-email') {
      return { success: false, msg: "Email format invalid." };
    }
    return { success: false, msg: error.message || "Unknown error occurred." };
  }
});

// Function to send welcome email using Firebase Admin SDK
/* exports.sendWelcomeEmail = onCall(async (request) => {
  try {
    const { email, name, password } = request.data;

    // Validate required parameters
    if (!email || !name || !password) {
      return { success: false, msg: "Email, name, and password are required." };
    }

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: email,
      subject: 'Welcome to Legacy PMS – Here are your account details',
      text: `Hello ${name.toUpperCase()},

Your Legacy PMS account is ready.

Login: https://legacy-pms.web.app/
Username: ${email}
Password: ${password}

Please update your password after your first login.

– Legacy PMS Team`,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Welcome email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return { success: false, msg: error.message || "Failed to send welcome email." };
  }
}); */

exports.sendWelcomeEmail = onCall(async (request) => {
  const { email, name, password } = request.data;
  return await sendWelcomeEmailHelper(email, name, password);
});

const sendWelcomeEmailHelper = async (email, name, password) => {
  try {
    if (!email || !name || !password) {
      return { success: false, msg: "Email, name, and password are required." };
    }

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: email,
      subject: 'Welcome to Legacy PMS – Here are your account details',
      text: `Hello ${name.toUpperCase()},\n\nYour Legacy PMS account is ready.\n\nLogin: https://legacy-pms.web.app/\nUsername: ${email}\nPassword: ${password}\n\nPlease update your password after your first login.\n\n– Legacy PMS Team`,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Welcome email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return { success: false, msg: error.message || "Failed to send welcome email." };
  }
};



// Function to send task assignment email
exports.sendTaskAssignmentEmail = onCall(async (request) => {
  try {
    const { 
      taskDetails, 
      employeeEmail, 
      employeeName, 
      tlEmail, 
      tlName, 
      customerName, 
      deadline,
      actualHours,
      taskId 
    } = request.data;

    if (!taskDetails || !employeeEmail || !employeeName || !tlEmail || !tlName || !customerName) {
      return { success: false, msg: "Missing required fields for task assignment email." };
    }

    // Send email to employee
    const employeeEmailResult = await sendTaskEmailToEmployee(
      employeeEmail,
      employeeName,
      taskDetails,
      customerName,
      deadline,
      actualHours,
      taskId
    );

    // Send email to TL
    const tlEmailResult = await sendTaskEmailToTL(
      tlEmail,
      tlName,
      employeeName,
      taskDetails,
      customerName,
      deadline,
      actualHours,
      taskId
    );

    return { 
      success: true, 
      employeeEmailSent: employeeEmailResult.success,
      tlEmailSent: tlEmailResult.success,
      employeeError: employeeEmailResult.error || null,
      tlError: tlEmailResult.error || null
    };
  } catch (error) {
    console.error("Error sending task assignment emails:", error);
    return { success: false, msg: error.message || "Unknown error occurred." };
  }
});

// Function to send task assignment email to employee
const sendTaskEmailToEmployee = async (email, employeeName, taskDetails, customerName, deadline, actualHours, taskId) => {
  try {
    // Format deadline as dd/mm/yyyy
    const formattedDeadline = deadline ? 
      new Date(deadline).toLocaleDateString('en-GB') : 'Not specified';
    
    // Format actual hours for display
    const formattedHours = actualHours || 'Not specified';
    
    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: email,
      subject: `New Task Assigned: ${taskDetails}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">New Task Assigned</h2>
          
          <p>Hi ${employeeName.toUpperCase()},</p>
          
          <p>You have been assigned "<strong>${taskDetails}</strong>" - <strong>${customerName.toUpperCase()}</strong>.</p>
          
          <p>Please do the status update in PMS as you complete the task for QC.</p>
          
          <p><strong>Deadline:</strong> ${formattedDeadline} - ${formattedHours}</p>
          
          <div style="margin: 30px 0;">
            <a href="https://legacy-pms.web.app/" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              View Task
            </a>
          </div>
          
          <p>Thank You,<br>Legacy PMS</p>
        </div>
      `,
      text: `Hi ${employeeName},

You have been assigned "${taskDetails}" - ${customerName}.

Please do the status update in PMS as you complete the task for QC.

Deadline: ${formattedDeadline} - ${formattedHours}

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Task assignment email sent to employee:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending task email to employee:', error);
    return { success: false, error: error.message };
  }
};

// Function to send task assignment email to TL
const sendTaskEmailToTL = async (email, tlName, employeeName, taskDetails, customerName, deadline, actualHours, taskId) => {
  try {
    // Format deadline as dd/mm/yyyy
    const formattedDeadline = deadline ? 
      new Date(deadline).toLocaleDateString('en-GB') : 'Not specified';
    
    // Format actual hours for display
    const formattedHours = actualHours || 'Not specified';
    
    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: email,
      subject: `New Task Assigned: ${taskDetails}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Task Assigned</h2>
          
          <p>Hi ${tlName.toUpperCase()},</p>
          
          <p>You need to monitor the task assigned to <strong>${employeeName.toUpperCase()}</strong> and follow the correct quality process.</p>
          
          <p><strong>Task:</strong> ${taskDetails.toUpperCase()}<br>
          <strong>Customer:</strong> ${customerName.toUpperCase()}<br>
          <strong>Assigned to:</strong> ${employeeName.toUpperCase()}<br>
          <strong>Deadline:</strong> ${formattedDeadline} - ${formattedHours}</p>
          
          <div style="margin: 30px 0;">
            <a href="https://legacy-pms.web.app/" 
               style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              View Task
            </a>
          </div>
          
          <p>Thank You,<br>Legacy PMS</p>
        </div>
      `,
      text: `Hi ${tlName},

You need to monitor the task assigned to ${employeeName} and follow the correct quality process.

Task: ${taskDetails}
Customer: ${customerName}
Assigned to: ${employeeName}
Deadline: ${formattedDeadline} - ${formattedHours}

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Task assignment email sent to TL:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending task email to TL:', error);
    return { success: false, error: error.message };
  }
};

// Function to send task reminder emails (12pm, 3pm, 4:30pm)
exports.sendTaskReminderEmails = onCall(async (request) => {
  try {
    const { reminderType } = request.data; // '12pm', '3pm', '4:30pm'
    
    if (!reminderType) {
      return { success: false, msg: "Reminder type is required." };
    }

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0); // 00:00:00 local time
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999); // 23:59:59.999 local time

    const startTimestamp = admin.firestore.Timestamp.fromDate(todayStart);
    const endTimestamp = admin.firestore.Timestamp.fromDate(todayEnd);
    // const now = new Date();
    // const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // // Get tasks created today (starting from 11 AM)
    // const localStart = new Date(today);
    // localStart.setHours(11, 0, 0, 0);

    // // Local end time 23:59:59.999
    // const localEnd = new Date(today);
    // localEnd.setHours(23, 59, 59, 999);

    // // Convert local start and end times to UTC by offsetting timezone offset
    // const utcStart = new Date(localStart.getTime() - localStart.getTimezoneOffset() * 60000);
    // const utcEnd = new Date(localEnd.getTime() - localEnd.getTimezoneOffset() * 60000);

    // // Convert to Firestore Timestamp objects
    // const startTimestamp = Timestamp.fromDate(utcStart);
    // const endTimestamp = Timestamp.fromDate(utcEnd);

    console.log('startOfDay (UTC Timestamp):', startTimestamp);
    console.log('endOfDay (UTC Timestamp):', endTimestamp);

    const tasksSnapshot = await db.collection('Tasks')
    .where('createdAt','>=', startTimestamp)
    .where('createdAt','<=', endTimestamp)
    .get();

    // const tasksSnapshot = await db.collection('Tasks').limit(5).get();
    // tasksSnapshot.forEach(doc => {
    // console.log(doc.id, doc.data().createdAt.toDate().toISOString());
    // });

    tasksSnapshot.forEach(doc => {
      console.log(doc.id, doc.data().createdAt.toDate().toISOString());
    });

    console.log('tasksSnapshot:', tasksSnapshot.docs.length);
      

    if (tasksSnapshot.empty) {
      return { success: true, msg: "No tasks found for today." };
    }

    const tasks = [];
    for (const doc of tasksSnapshot.docs) {
      const taskData = doc.data();
      const taskId = doc.id;
      
      // Get user details
      const employeeDoc = await db.collection('Users').doc(taskData.employee).get();
      const supervisorDoc = await db.collection('Users').doc(taskData.supervisor).get();
      const customerDoc = await db.collection('Customers').doc(taskData.customer).get();
      
      if (employeeDoc.exists && supervisorDoc.exists && customerDoc.exists) {
        tasks.push({
          id: taskId,
          ...taskData,
          employee: employeeDoc.data(),
          supervisor: supervisorDoc.data(),
          customer: customerDoc.data()
        });
      }
    }

    let emailsSent = 0;
    let errors = [];

    for (const task of tasks) {
      try {
        const taskStatus = task.status;
        let shouldSendEmail = false;
        let emailType = '';

        // Determine if email should be sent based on reminder type and task status
        switch (reminderType) {
          case '12pm':
            if (taskStatus === 'Not yet started') {
              shouldSendEmail = true;
              emailType = 'not_started';
            } else if (taskStatus === 'On hold' || taskStatus === 'Need discussion') {
              shouldSendEmail = true;
              emailType = 'discussion';
            }
            break;
          case '3pm':
            if (taskStatus !== 'Completed') {
              shouldSendEmail = true;
              emailType = 'general_reminder';
            }
            break;
          case '4:30pm':
            if (taskStatus !== 'Completed') {
              shouldSendEmail = true;
              emailType = 'delayed';
            }
            break;
        }

        if (shouldSendEmail) {
          // Send email to employee
          const employeeEmailResult = await sendTaskReminderEmail(
            task.employee.email,
            task.employee.name,
            task.details,
            task.customer.name,
            task.actualHours,
            emailType,
            reminderType
          );

          // Send email to TL
          const tlEmailResult = await sendTaskReminderEmail(
            task.supervisor.email,
            task.supervisor.name,
            task.details,
            task.customer.name,
            task.actualHours,
            emailType,
            reminderType,
            task.employee.name
          );

          if (employeeEmailResult.success && tlEmailResult.success) {
            emailsSent += 2;
          } else {
            errors.push(`Task ${task.id}: Employee email: ${employeeEmailResult.success ? 'sent' : 'failed'}, TL email: ${tlEmailResult.success ? 'sent' : 'failed'}`);
          }
        }
      } catch (error) {
        errors.push(`Task ${task.id}: ${error.message}`);
      }
    }

    return { 
      success: true, 
      emailsSent,
      errors: errors.length > 0 ? errors : null,
      msg: `Reminder emails processed. ${emailsSent} emails sent.`
    };
  } catch (error) {
    console.error("Error sending task reminder emails:", error);
    return { success: false, msg: error.message || "Unknown error occurred." };
  }
});

// Function to send QC ready for review email (Employee to TL)
exports.sendQCReadyEmail = onCall(async (request) => {
  try {
    const {
      taskDetails,
      taskId,
      tlEmail,
      tlName,
      employeeName,
      customerName
    } = request.data;

    if (!taskDetails || !taskId || !tlEmail || !tlName || !employeeName || !customerName) {
      return { success: false, msg: "Missing required fields for QC ready email." };
    }

    const subject = `Task Ready for Quality Check: ${taskDetails}`;
    const bodyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Quality Check Notification</h2>

        <p>Hi ${tlName.toUpperCase()},</p>

        <p>The task "<strong>${taskDetails}</strong>" is ready for review.</p>
        <p>Please perform the quality check and share your feedback.</p>

        <p><strong>Task:</strong> ${taskDetails}<br>
        <strong>Customer:</strong> ${customerName}<br>
        <strong>Submitted by:</strong> ${employeeName}</p>

        <div style="margin: 20px 0;">
          <a href="https://legacy-pms.web.app/" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Task</a>
        </div>

        <p>Regards,<br>Legacy PMS Team</p>
      </div>
    `;

    const bodyText = `Hi ${tlName.toUpperCase()},

The task "${taskDetails}" is ready for review.
Please perform the quality check and share your feedback.

Task: ${taskDetails}
Customer: ${customerName}
Submitted by: ${employeeName}

Review Task: https://legacy-pms.web.app/

Regards,
Legacy PMS Team`;

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: tlEmail,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('QC ready email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending QC ready email:', error);
    return { success: false, error: error.message };
  }
});

// Function to send task completion email (TL to Employee)
exports.sendTaskCompletionEmail = onCall(async (request) => {
  try {
    const {
      taskDetails,
      taskId,
      employeeEmail,
      employeeName,
      customerName
    } = request.data;

    if (!taskDetails || !taskId || !employeeEmail || !employeeName || !customerName) {
      return { success: false, msg: "Missing required fields for task completion email." };
    }

    const subject = `Task Completed: ${taskDetails}`;
    const bodyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">Task Completion Confirmation</h2>

        <p>Hi ${employeeName.toUpperCase()},</p>

        <p>The task "<strong>${taskDetails}</strong>" has been marked as completed.</p>
        <p>Great job! 🎉</p>

        <p><strong>Task:</strong> ${taskDetails}<br>
        <strong>Customer:</strong> ${customerName}</p>

        <div style="margin: 20px 0;">
          <a href="https://legacy-pms.web.app/" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Completed Task</a>
        </div>

        <p>Thanks,<br>Legacy PMS Team</p>
      </div>
    `;

    const bodyText = `Hi ${employeeName.toUpperCase()},

The task "${taskDetails}" has been marked as completed.
Great job! 🎉

Task: ${taskDetails}
Customer: ${customerName}

View Completed Task: https://legacy-pms.web.app/

Thanks,
Legacy PMS Team`;

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: employeeEmail,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Task completion email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending task completion email:', error);
    return { success: false, error: error.message };
  }
});

// Function to send rework request email (TL to Employee)
exports.sendReworkRequestEmail = onCall(async (request) => {
  try {
    const {
      taskDetails,
      taskId,
      employeeEmail,
      employeeName,
      customerName,
      feedback
    } = request.data;

    if (!taskDetails || !taskId || !employeeEmail || !employeeName || !customerName) {
      return { success: false, msg: "Missing required fields for rework request email." };
    }

    const subject = `Rework Required: ${taskDetails}`;
    const bodyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #ffc107;">Rework Request</h2>

        <p>Hi ${employeeName.toUpperCase()},</p>

        <p>During review, a few updates were requested on "<strong>${taskDetails}</strong>".</p>
        <p>Please check the comments and make necessary corrections.</p>

        <p><strong>Task:</strong> ${taskDetails}<br>
        <strong>Customer:</strong> ${customerName}</p>

        ${feedback ? `<div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0;">
          <strong>Feedback:</strong><br>
          ${feedback}
        </div>` : ''}

        <div style="margin: 20px 0;">
          <a href="https://legacy-pms.web.app/" style="background-color: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Feedback</a>
        </div>

        <p>Thank you,<br>Legacy PMS Team</p>
      </div>
    `;

    const bodyText = `Hi ${employeeName.toUpperCase()},

During review, a few updates were requested on "${taskDetails}".
Please check the comments and make necessary corrections.

Task: ${taskDetails}
Customer: ${customerName}

${feedback ? `Feedback: ${feedback}` : ''}

View Feedback: https://legacy-pms.web.app/

Thank you,
Legacy PMS Team`;

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: employeeEmail,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Rework request email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending rework request email:', error);
    return { success: false, error: error.message };
  }
});

// Function to send individual task reminder email
const sendTaskReminderEmail = async (email, name, taskDetails, customerName, actualHours, emailType, reminderType, employeeName = null) => {
  try {
    let subject = '';
    let bodyText = '';
    let bodyHtml = '';

    // Format actual hours for display
    const formattedHours = actualHours || 'Not specified';

    // Calculate deadline (assuming 8 hours from 11 AM = 7 PM)
    const deadline = new Date();
    deadline.setHours(19, 0, 0, 0); // 7 PM
    // const formattedDeadline = deadline.toLocaleDateString('en-GB') + ' - 7:00 PM';
    const formattedDeadline = deadline.toLocaleDateString('en-GB') + ' - ' + deadline.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });


    switch (emailType) {
      case 'not_started':
        subject = `Reminder: Task "${taskDetails}" Yet to Start`;
        bodyText = `Hi ${name.toUpperCase()},

We noticed that the task "${taskDetails}" hasn't been started yet.
Please begin at the earliest to stay on track for the deadline ${formattedDeadline}.

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`;

        bodyHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Task Reminder</h2>
            
            <p>Hi ${name.toUpperCase()},</p>
            
            <p>We noticed that the task "<strong>${taskDetails}</strong>" hasn't been started yet.</p>
            
            <p>Please begin at the earliest to stay on track for the deadline <strong>${formattedDeadline}</strong>.</p>
            
            <div style="margin: 30px 0;">
              <a href="https://legacy-pms.web.app/" 
                 style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                View Task
              </a>
            </div>
            
            <p>Thank You,<br>Legacy PMS</p>
          </div>
        `;
        break;

      case 'discussion':
        subject = `Reminder: Task "${taskDetails}" Needs Discussion`;
        bodyText = `Hi ${name.toUpperCase()},

The task "${taskDetails}" requires discussion to proceed.
Please coordinate with the team to resolve any issues and move forward.

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`;

        bodyHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Task Reminder</h2>
            
            <p>Hi ${name.toUpperCase()},</p>
            
            <p>The task "<strong>${taskDetails}</strong>" requires discussion to proceed.</p>
            
            <p>Please coordinate with the team to resolve any issues and move forward.</p>
            
            <div style="margin: 30px 0;">
              <a href="https://legacy-pms.web.app/" 
                 style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                View Task
              </a>
            </div>
            
            <p>Thank You,<br>Legacy PMS</p>
          </div>
        `;
        break;

      case 'general_reminder':
        subject = `Reminder: Task "${taskDetails}" Status Update Required`;
        bodyText = `Hi ${name.toUpperCase()},

This is a reminder to update the status of task "${taskDetails}" - ${customerName.toUpperCase()}.
Please ensure the task is progressing as expected.

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`;

        bodyHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Task Reminder</h2>
            
            <p>Hi ${name.toUpperCase()},</p>
            
            <p>This is a reminder to update the status of task "<strong>${taskDetails}</strong>" - <strong>${customerName.toUpperCase()}</strong>.</p>
            
            <p>Please ensure the task is progressing as expected.</p>
            
            <div style="margin: 30px 0;">
              <a href="https://legacy-pms.web.app/" 
                 style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                View Task
              </a>
            </div>
            
            <p>Thank You,<br>Legacy PMS</p>
          </div>
        `;
        break;

      case 'delayed':
        subject = `Delayed Task Reminder: "${taskDetails}"`;
        bodyText = `Hi ${name.toUpperCase()},

The task "${taskDetails}" - ${customerName.toUpperCase()} appears to be delayed.
${employeeName ? `Assigned to: ${employeeName.toUpperCase()}` : ''}
Please take immediate action to complete this task.

View Task: https://legacy-pms.web.app/

Thank You,
Legacy PMS`;

        bodyHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Delayed Task Reminder</h2>
            
            <p>Hi ${name.toUpperCase()},</p>
            
            <p>The task "<strong>${taskDetails}</strong>" - <strong>${customerName.toUpperCase()}</strong> appears to be delayed.</p>
            
            ${employeeName ? `<p><strong>Assigned to:</strong> ${employeeName.toUpperCase()}</p>` : ''}
            
            <p>Please take immediate action to complete this task.</p>
            
            <div style="margin: 30px 0;">
              <a href="https://legacy-pms.web.app/" 
                 style="background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                View Task
              </a>
            </div>
            
            <p>Thank You,<br>Legacy PMS</p>
          </div>
        `;
        break;
    }

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: email,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log(`Task reminder email sent (${reminderType}):`, info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error(`Error sending task reminder email (${reminderType}):`, error);
    return { success: false, error: error.message };
  }
};

// Scheduled function to send 12pm reminder emails
exports.scheduledTaskReminder12pm = onSchedule({
  schedule: "0 12 * * 1-5", // Every weekday at 12:00 PM (UTC)
  timeZone: "Asia/Kolkata", // Adjust to your timezone
}, async (event) => {
  try {
    console.log("Running scheduled 12pm task reminder emails...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    const startTimestamp = admin.firestore.Timestamp.fromDate(todayStart);
    const endTimestamp = admin.firestore.Timestamp.fromDate(todayEnd);

    const tasksSnapshot = await db.collection('Tasks')
      .where('createdAt','>=', startTimestamp)
      .where('createdAt','<=', endTimestamp)
      .get();

    if (tasksSnapshot.empty) {
      console.log("No tasks found for today - 12pm reminder");
      return;
    }

    const tasks = [];
    for (const doc of tasksSnapshot.docs) {
      const taskData = doc.data();
      const taskId = doc.id;

      // Get user details
      const employeeDoc = await db.collection('Users').doc(taskData.employee).get();
      const supervisorDoc = await db.collection('Users').doc(taskData.supervisor).get();
      const customerDoc = await db.collection('Customers').doc(taskData.customer).get();

      if (employeeDoc.exists && supervisorDoc.exists && customerDoc.exists) {
        tasks.push({
          id: taskId,
          ...taskData,
          employee: employeeDoc.data(),
          supervisor: supervisorDoc.data(),
          customer: customerDoc.data()
        });
      }
    }

    let emailsSent = 0;
    let errors = [];

    for (const task of tasks) {
      try {
        const taskStatus = task.status;
        let shouldSendEmail = false;
        let emailType = '';

        // 12pm logic: send for not started or discussion needed tasks
        if (taskStatus === 'Not yet started') {
          shouldSendEmail = true;
          emailType = 'not_started';
        } else if (taskStatus === 'On hold' || taskStatus === 'Need discussion') {
          shouldSendEmail = true;
          emailType = 'discussion';
        }

        if (shouldSendEmail) {
          // Send email to employee
          const employeeEmailResult = await sendTaskReminderEmail(
            task.employee.email,
            task.employee.name,
            task.details,
            task.customer.name,
            task.actualHours,
            emailType,
            '12pm'
          );

          // Send email to TL
          const tlEmailResult = await sendTaskReminderEmail(
            task.supervisor.email,
            task.supervisor.name,
            task.details,
            task.customer.name,
            task.actualHours,
            emailType,
            '12pm',
            task.employee.name
          );

          if (employeeEmailResult.success && tlEmailResult.success) {
            emailsSent += 2;
          } else {
            errors.push(`Task ${task.id}: Employee email: ${employeeEmailResult.success ? 'sent' : 'failed'}, TL email: ${tlEmailResult.success ? 'sent' : 'failed'}`);
          }
        }
      } catch (error) {
        errors.push(`Task ${task.id}: ${error.message}`);
      }
    }

    console.log(`12pm reminder emails completed. ${emailsSent} emails sent.`);
    if (errors.length > 0) {
      console.error("12pm reminder errors:", errors);
    }
  } catch (error) {
    console.error("Error in scheduled 12pm task reminder:", error);
  }
});

// Scheduled function to send 3pm reminder emails
exports.scheduledTaskReminder3pm = onSchedule({
  schedule: "0 15 * * 1-5", // Every weekday at 3:00 PM (UTC)
  timeZone: "Asia/Kolkata", // Adjust to your timezone
}, async (event) => {
  try {
    console.log("Running scheduled 3pm task reminder emails...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    const startTimestamp = admin.firestore.Timestamp.fromDate(todayStart);
    const endTimestamp = admin.firestore.Timestamp.fromDate(todayEnd);

    const tasksSnapshot = await db.collection('Tasks')
      .where('createdAt','>=', startTimestamp)
      .where('createdAt','<=', endTimestamp)
      .get();

    if (tasksSnapshot.empty) {
      console.log("No tasks found for today - 3pm reminder");
      return;
    }

    const tasks = [];
    for (const doc of tasksSnapshot.docs) {
      const taskData = doc.data();
      const taskId = doc.id;

      // Get user details
      const employeeDoc = await db.collection('Users').doc(taskData.employee).get();
      const supervisorDoc = await db.collection('Users').doc(taskData.supervisor).get();
      const customerDoc = await db.collection('Customers').doc(taskData.customer).get();

      if (employeeDoc.exists && supervisorDoc.exists && customerDoc.exists) {
        tasks.push({
          id: taskId,
          ...taskData,
          employee: employeeDoc.data(),
          supervisor: supervisorDoc.data(),
          customer: customerDoc.data()
        });
      }
    }

    let emailsSent = 0;
    let errors = [];

    for (const task of tasks) {
      try {
        const taskStatus = task.status;

        // 3pm logic: send for all incomplete tasks
        if (taskStatus !== 'Completed') {
          // Send email to employee
          const employeeEmailResult = await sendTaskReminderEmail(
            task.employee.email,
            task.employee.name,
            task.details,
            task.customer.name,
            task.actualHours,
            'general_reminder',
            '3pm'
          );

          // Send email to TL
          const tlEmailResult = await sendTaskReminderEmail(
            task.supervisor.email,
            task.supervisor.name,
            task.details,
            task.customer.name,
            task.actualHours,
            'general_reminder',
            '3pm',
            task.employee.name
          );

          if (employeeEmailResult.success && tlEmailResult.success) {
            emailsSent += 2;
          } else {
            errors.push(`Task ${task.id}: Employee email: ${employeeEmailResult.success ? 'sent' : 'failed'}, TL email: ${tlEmailResult.success ? 'sent' : 'failed'}`);
          }
        }
      } catch (error) {
        errors.push(`Task ${task.id}: ${error.message}`);
      }
    }

    console.log(`3pm reminder emails completed. ${emailsSent} emails sent.`);
    if (errors.length > 0) {
      console.error("3pm reminder errors:", errors);
    }
  } catch (error) {
    console.error("Error in scheduled 3pm task reminder:", error);
  }
});

// Scheduled function to send 4:30pm reminder emails
exports.scheduledTaskReminder430pm = onSchedule({
  schedule: "30 16 * * 1-5", // Every weekday at 4:30 PM (UTC)
  timeZone: "Asia/Kolkata", // Adjust to your timezone
}, async (event) => {
  try {
    console.log("Running scheduled 4:30pm task reminder emails...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    const startTimestamp = admin.firestore.Timestamp.fromDate(todayStart);
    const endTimestamp = admin.firestore.Timestamp.fromDate(todayEnd);

    const tasksSnapshot = await db.collection('Tasks')
      .where('createdAt','>=', startTimestamp)
      .where('createdAt','<=', endTimestamp)
      .get();

    if (tasksSnapshot.empty) {
      console.log("No tasks found for today - 4:30pm reminder");
      return;
    }

    const tasks = [];
    for (const doc of tasksSnapshot.docs) {
      const taskData = doc.data();
      const taskId = doc.id;

      // Get user details
      const employeeDoc = await db.collection('Users').doc(taskData.employee).get();
      const supervisorDoc = await db.collection('Users').doc(taskData.supervisor).get();
      const customerDoc = await db.collection('Customers').doc(taskData.customer).get();

      if (employeeDoc.exists && supervisorDoc.exists && customerDoc.exists) {
        tasks.push({
          id: taskId,
          ...taskData,
          employee: employeeDoc.data(),
          supervisor: supervisorDoc.data(),
          customer: customerDoc.data()
        });
      }
    }

    let emailsSent = 0;
    let errors = [];

    for (const task of tasks) {
      try {
        const taskStatus = task.status;

        // 4:30pm logic: send for all incomplete tasks (delayed reminder)
        if (taskStatus !== 'Completed') {
          // Send email to employee
          const employeeEmailResult = await sendTaskReminderEmail(
            task.employee.email,
            task.employee.name,
            task.details,
            task.customer.name,
            task.actualHours,
            'delayed',
            '4:30pm'
          );

          // Send email to TL
          const tlEmailResult = await sendTaskReminderEmail(
            task.supervisor.email,
            task.supervisor.name,
            task.details,
            task.customer.name,
            task.actualHours,
            'delayed',
            '4:30pm',
            task.employee.name
          );

          if (employeeEmailResult.success && tlEmailResult.success) {
            emailsSent += 2;
          } else {
            errors.push(`Task ${task.id}: Employee email: ${employeeEmailResult.success ? 'sent' : 'failed'}, TL email: ${tlEmailResult.success ? 'sent' : 'failed'}`);
          }
        }
      } catch (error) {
        errors.push(`Task ${task.id}: ${error.message}`);
      }
    }

    console.log(`4:30pm reminder emails completed. ${emailsSent} emails sent.`);
    if (errors.length > 0) {
      console.error("4:30pm reminder errors:", errors);
    }
  } catch (error) {
    console.error("Error in scheduled 4:30pm task reminder:", error);
  }
});

exports.getUserUidByEmail = functions.https.onCall(async (data, context) => {
  const email = data.email;
  if (!email) {
    throw new functions.https.HttpsError('invalid-argument', 'Email is required');
  }
  try {
    const userRecord = await admin.auth().getUserByEmail(email);
    return { uid: userRecord.uid };
  } catch (err) {
    throw new functions.https.HttpsError('not-found', 'No Auth user for that email');
  }
});

// Function to check for users still logged in after 8:00 PM and send notifications
exports.checkLoggedInUsersAfter8PM = onCall(async (request) => {
  try {
    console.log("Checking for users still logged in after 8:00 PM...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    // Get all users
    const usersSnapshot = await db.collection('Users').get();
    const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    let emailsSent = 0;
    const errors = [];
    const loggedInUsers = [];

    // Check each user's punch status for today
    for (const user of users) {
      try {
        // Skip admin users (they don't need to punch in/out)
        if (user.role && user.role.toLowerCase().includes('admin')) {
          continue;
        }

        // Get today's punches for this user
        const punchesSnapshot = await db.collection('Punches')
          .where('uId', '==', user.id)
          .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(todayStart))
          .where('createdAt', '<=', admin.firestore.Timestamp.fromDate(todayEnd))
          .orderBy('createdAt', 'desc')
          .get();

        if (punchesSnapshot.empty) {
          // No punches today, user is not logged in
          continue;
        }

        // Get the latest punch for today
        const latestPunch = punchesSnapshot.docs[0].data();

        // If latest punch is punch-in (true), user is still logged in
        if (latestPunch.punchIn === true) {
          loggedInUsers.push({
            user: user,
            punchInTime: latestPunch.createdAt.toDate()
          });

          // Send notification emails
          const emailResult = await sendLoggedInNotification(user);
          if (emailResult.success) {
            emailsSent++;
          } else {
            errors.push(`User ${user.name}: ${emailResult.error}`);
          }
        }
      } catch (error) {
        errors.push(`User ${user.name || user.id}: ${error.message}`);
      }
    }

    console.log(`Logged-in users check completed. ${emailsSent} notifications sent for ${loggedInUsers.length} users still logged in.`);

    return {
      success: true,
      loggedInUsersCount: loggedInUsers.length,
      emailsSent,
      errors: errors.length > 0 ? errors : null,
      msg: `Found ${loggedInUsers.length} users still logged in. ${emailsSent} notifications sent.`
    };
  } catch (error) {
    console.error("Error checking logged-in users:", error);
    return { success: false, msg: error.message || "Unknown error occurred." };
  }
});

// Scheduled function to automatically check for logged-in users at 8:00 PM
exports.scheduledLoggedInUsersCheck = onSchedule({
  schedule: "0 20 * * 1-5", // Every weekday at 8:00 PM (UTC)
  timeZone: "Asia/Kolkata", //  Standard Time
}, async (event) => {
  try {
    console.log("Running scheduled logged-in users check at 8:00 PM...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    // Get all users
    const usersSnapshot = await db.collection('Users').get();
    const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    let emailsSent = 0;
    const errors = [];
    const loggedInUsers = [];

    // Check each user's punch status for today
    for (const user of users) {
      try {
        // Skip admin users (they don't need to punch in/out)
        if (user.role && user.role.toLowerCase().includes('admin')) {
          continue;
        }

        // Get today's punches for this user
        const punchesSnapshot = await db.collection('Punches')
          .where('uId', '==', user.id)
          .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(todayStart))
          .where('createdAt', '<=', admin.firestore.Timestamp.fromDate(todayEnd))
          .orderBy('createdAt', 'desc')
          .get();

        if (punchesSnapshot.empty) {
          // No punches today, user is not logged in
          continue;
        }

        // Get the latest punch for today
        const latestPunch = punchesSnapshot.docs[0].data();

        // If latest punch is punch-in (true), user is still logged in
        if (latestPunch.punchIn === true) {
          loggedInUsers.push({
            user: user,
            punchInTime: latestPunch.createdAt.toDate()
          });

          // Send notification emails
          const emailResult = await sendLoggedInNotification(user);
          if (emailResult.success) {
            emailsSent++;
          } else {
            errors.push(`User ${user.name}: ${emailResult.error}`);
          }
        }
      } catch (error) {
        errors.push(`User ${user.name || user.id}: ${error.message}`);
      }
    }

    console.log(`Scheduled logged-in users check completed. ${emailsSent} notifications sent for ${loggedInUsers.length} users still logged in.`);
    if (errors.length > 0) {
      console.error("Logged-in users check errors:", errors);
    }
  } catch (error) {
    console.error("Error in scheduled logged-in users check:", error);
  }
});

// Helper function to send logged-in notification emails
const sendLoggedInNotification = async (user) => {
  try {
    // Get all users with MTS roles for notification
    const mtsUsersSnapshot = await db.collection('Users')
      .where('role', 'in', ['Manager', 'TL', 'Supervisor', 'Manager , TL , Supervisor'])
      .get();

    const mtsEmails = mtsUsersSnapshot.docs
      .map(doc => doc.data().email)
      .filter(email => email && email.trim() !== '');

    // Email to the logged-in user
    const userEmailResult = await sendLoggedInUserEmail(user);

    // Email to MTS recipients
    const mtsEmailResult = await sendLoggedInMTSEmail(user, mtsEmails);

    return {
      success: userEmailResult.success && mtsEmailResult.success,
      userEmailSent: userEmailResult.success,
      mtsEmailSent: mtsEmailResult.success,
      error: userEmailResult.error || mtsEmailResult.error
    };
  } catch (error) {
    console.error('Error sending logged-in notification:', error);
    return { success: false, error: error.message };
  }
};

// Function to send email to the logged-in user
const sendLoggedInUserEmail = async (user) => {
  try {
    const currentTime = new Date().toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    });

    const subject = 'Reminder: You are still logged in to Legacy PMS';

    const bodyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Still Logged In Reminder</h2>

        <p>Hi ${user.name.toUpperCase()},</p>

        <p>This is a reminder that you are still logged in to the Legacy PMS system.</p>

        <p><strong>Current Time:</strong> ${currentTime} (Indian Standard Time)</p>
        <p><strong>Office Hours:</strong> Please remember to log out after 8:00 PM</p>

        <p>If you have finished your work for the day, please log out from the system.</p>

        <p>If you are still working, please ignore this message.</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff;">
          <p style="margin: 0;"><strong>Access PMS:</strong> <a href="https://legacy-pms.web.app/" style="color: #007bff;">https://legacy-pms.web.app/</a></p>
        </div>

        <p>Thank you,<br>Legacy PMS Team</p>
      </div>
    `;

    const bodyText = `Hi ${user.name.toUpperCase()},

This is a reminder that you are still logged in to the Legacy PMS system.

Current Time: ${currentTime} (Indian Standard Time)
Office Hours: Please remember to log out after 8:00 PM

If you have finished your work for the day, please log out from the system.
If you are still working, please ignore this message.

Access PMS: https://legacy-pms.web.app/

Thank you,
Legacy PMS Team`;

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: user.email,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Logged-in user notification email sent:', info.messageId);

    return { success: true, message: `Email sent: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending logged-in user email:', error);
    return { success: false, error: error.message };
  }
};

// Function to send email to MTS (Manager, TL, Supervisor)
const sendLoggedInMTSEmail = async (user, mtsEmails) => {
  try {
    if (!mtsEmails || mtsEmails.length === 0) {
      console.log('No MTS emails found for notification');
      return { success: true, message: 'No MTS recipients found' };
    }

    const currentTime = new Date().toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    });

    const subject = `Alert: ${user.name} has not logged out after 8:00 PM`;

    const bodyHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #d73527;">User Still Logged In Alert</h2>

        <p>Dear Team,</p>

        <p>This is to inform you that the following user has not logged out from the Legacy PMS system after 8:00 PM:</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #fff3cd; border-left: 4px solid #ffc107;">
          <p style="margin: 0;"><strong>User:</strong> ${user.name.toUpperCase()}</p>
          <p style="margin: 5px 0 0 0;"><strong>Email:</strong> ${user.email}</p>
          <p style="margin: 5px 0 0 0;"><strong>Role:</strong> ${user.role || 'Not specified'}</p>
          <p style="margin: 5px 0 0 0;"><strong>Current Time:</strong> ${currentTime} (Indian Standard Time)</p>
        </div>

        <p>Please follow up with the user if necessary to ensure proper logout procedures are followed.</p>

        <p>This is an automated notification sent at 8:00 PM daily for users who remain logged in.</p>

        <div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff;">
          <p style="margin: 0;"><strong>Access PMS:</strong> <a href="https://legacy-pms.web.app/" style="color: #007bff;">https://legacy-pms.web.app/</a></p>
        </div>

        <p>Best regards,<br>Legacy PMS System</p>
      </div>
    `;

    const bodyText = `Dear Team,

This is to inform you that the following user has not logged out from the Legacy PMS system after 8:00 PM:

User: ${user.name.toUpperCase()}
Email: ${user.email}
Role: ${user.role || 'Not specified'}
Current Time: ${currentTime} (Indian Standard Time)

Please follow up with the user if necessary to ensure proper logout procedures are followed.

This is an automated notification sent at 8:00 PM daily for users who remain logged in.

Access PMS: https://legacy-pms.web.app/

Best regards,
Legacy PMS System`;

    const mailOptions = {
      from: '"Legacy PMS Team" <<EMAIL>>',
      to: mtsEmails,
      subject: subject,
      html: bodyHtml,
      text: bodyText
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('MTS notification email sent:', info.messageId);

    return { success: true, message: `Email sent to ${mtsEmails.length} MTS recipients: ${info.messageId}` };
  } catch (error) {
    console.error('Error sending MTS notification email:', error);
    return { success: false, error: error.message };
  }
};

// Test function to manually trigger logged-in users check (for testing purposes)
exports.testLoggedInUsersCheck = onCall(async (request) => {
  try {
    console.log("Manual test: Checking for users still logged in...");

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
    const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);

    // Get all users
    const usersSnapshot = await db.collection('Users').get();
    const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    let emailsSent = 0;
    const errors = [];
    const loggedInUsers = [];

    // Check each user's punch status for today
    for (const user of users) {
      try {
        // Skip admin users (they don't need to punch in/out)
        if (user.role && user.role.toLowerCase().includes('admin')) {
          continue;
        }

        // Get today's punches for this user
        const punchesSnapshot = await db.collection('Punches')
          .where('uId', '==', user.id)
          .where('createdAt', '>=', admin.firestore.Timestamp.fromDate(todayStart))
          .where('createdAt', '<=', admin.firestore.Timestamp.fromDate(todayEnd))
          .orderBy('createdAt', 'desc')
          .get();

        if (punchesSnapshot.empty) {
          // No punches today, user is not logged in
          continue;
        }

        // Get the latest punch for today
        const latestPunch = punchesSnapshot.docs[0].data();

        // If latest punch is punch-in (true), user is still logged in
        if (latestPunch.punchIn === true) {
          loggedInUsers.push({
            user: user,
            punchInTime: latestPunch.createdAt.toDate()
          });

          // For testing, we'll just log instead of sending emails
          console.log(`User still logged in: ${user.name} (${user.email})`);
          emailsSent++; // Simulate email sent
        }
      } catch (error) {
        errors.push(`User ${user.name || user.id}: ${error.message}`);
      }
    }

    console.log(`Test completed. Found ${loggedInUsers.length} users still logged in.`);

    return {
      success: true,
      loggedInUsersCount: loggedInUsers.length,
      loggedInUsers: loggedInUsers.map(item => ({
        name: item.user.name,
        email: item.user.email,
        role: item.user.role,
        punchInTime: item.punchInTime
      })),
      emailsSent,
      errors: errors.length > 0 ? errors : null,
      msg: `Test: Found ${loggedInUsers.length} users still logged in. ${emailsSent} notifications would be sent.`
    };
  } catch (error) {
    console.error("Error in test logged-in users check:", error);
    return { success: false, msg: error.message || "Unknown error occurred." };
  }
});

exports.updateUserEmail = onCall(async (request) => {
  try {
    console.log("IN UPDATE USER EMAIL");
    
    const { uid, newEmail, currentEmail } = request.data;
    
    // Validate input
    if (!uid || !newEmail) {
      return {
        success: false,
        msg: "UID and new email are required",
        code: "invalid-argument"
      };
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      return {
        success: false,
        msg: "Invalid email format",
        code: "invalid-email"
      };
    }
    
    try {
      // Check if new email already exists in Auth
      try {
        await auth.getUserByEmail(newEmail);
        return {
          success: false,
          msg: "Email already exists in authentication",
          code: "email-already-exists"
        };
      } catch (error) {
        // Email doesn't exist, which is good - we can proceed
        if (error.code !== 'auth/user-not-found') {
          throw error;
        }
      }
      
      // Update email in Firebase Authentication
      await auth.updateUser(uid, {
        email: newEmail
      });
      console.log(`✅ Successfully updated email for user ${uid} to ${newEmail}`);
      
      // Update email in Firestore Users collection
      await db.collection('Users').doc(uid).update({
        email: newEmail,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      console.log(`✅ Successfully updated Firestore email for user ${uid}`);
      
      return {
        success: true,
        msg: "Email updated successfully",
        uid: uid,
        oldEmail: currentEmail,
        newEmail: newEmail
      };
      
    } catch (error) {
      console.error("❌ Error updating user email:", error.message);
      return {
        success: false,
        msg: error.message,
        code: error.code
      };
    }
    
  } catch (error) {
    console.error("❌ Unexpected error in updateUserEmail:", error.message);
    return {
      success: false,
      msg: "An unexpected error occurred",
      code: "internal-error"
    };
  }
});
