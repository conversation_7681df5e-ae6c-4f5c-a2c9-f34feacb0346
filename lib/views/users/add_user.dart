// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/common/multiselectbuttonformfield.dart';
import 'package:legacy_pms/controller/homectrl.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class AddUserDialog extends StatefulWidget {
  const AddUserDialog({super.key});

  @override
  State<AddUserDialog> createState() => _AddUserDialogState();
}

class _AddUserDialogState extends State<AddUserDialog> {
  final nameCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final phoneCtrl = TextEditingController();
  final designationCtrl = TextEditingController();

  String? selectedRole;
  List<String>? selectedCustomers = [];
  String? selectedShift;
  List<String> selectedPermissions = [];
  List<String> selectedTasks = [];

  bool submittingUser = false;

  List<String> roles = [];
  List<String> customers = [];

  @override
  void initState() {
    super.initState();
    loadRoles();
  }

  Future<void> loadRoles() async {
    fetchCombinedRoleTitles().then((list) {
      setState(() {
        roles = list;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    customers = Get.find<HomeCtrl>().customers.map((e) => e.name).toList();
    final List<DropdownItem<String>> customerItems = customers
        .map(
          (customer) => DropdownItem<String>(value: customer, label: customer),
        )
        .toList();
    return AlertDialog(
      backgroundColor: popupBgColor,
      title: const Text("Add User"),
      insetPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
      // shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Name TextField
            TextField(
              controller: nameCtrl,
              decoration: const InputDecoration(
                labelText: "Name",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: designationCtrl,
              decoration: const InputDecoration(
                labelText: "Designation",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            // Email TextField
            TextField(
              controller: emailCtrl,
              decoration: const InputDecoration(
                labelText: "Email",
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: phoneCtrl,
              decoration: const InputDecoration(
                labelText: "Phone Number",
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 12),
            // Role Dropdown
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: "Role",
                border: OutlineInputBorder(),
              ),
              value: selectedRole,
              onChanged: (val) => setState(() => selectedRole = val),
              items: roles
                  .map(
                    (role) => DropdownMenuItem(value: role, child: Text(role)),
                  )
                  .toList(),
            ),
            const SizedBox(height: 12),

            // Customers Dropdown
            MultiSelectButtonFormField(
              labelText: 'Customers',
              items: customerItems,
              initialValue: selectedCustomers ?? [],
              onChanged: (selectedList) {
                setState(() {
                  selectedCustomers = selectedList;
                });
              },
              decoration: const InputDecoration(border: OutlineInputBorder()),
              chipDecoration: const ChipDecoration(
                padding: EdgeInsets.all(20),
                labelStyle: TextStyle(color: Colors.black),
              ),

              // validator: (selected) {
              //   if (selected == null || selected.isEmpty) {
              //     return 'Please select at least one customer';
              //   }
              //   return null;
              // },
            ),

            const SizedBox(height: 12),

            // Permissions multi-select
            // _MultiSelectDropdown(
            //   title: "Permissions",
            //   options: permissions,
            //   selected: selectedPermissions,
            //   onChanged: (newList) =>
            //       setState(() => selectedPermissions = newList),
            // ),
            // const SizedBox(height: 12),
            // // Task list multi-select
            // _MultiSelectDropdown(
            //   title: "Task List",
            //   options: tasks,
            //   selected: selectedTasks,
            //   onChanged: (newList) => setState(() => selectedTasks = newList),
            // ),
            // const SizedBox(height: 12),
            // Shift Dropdown
            // DropdownButtonFormField<String>(
            //   decoration: const InputDecoration(
            //     labelText: "Shift",
            //     border: OutlineInputBorder(),
            //   ),
            //   value: selectedShift,
            //   onChanged: (val) => setState(() => selectedShift = val),
            //   items: shift
            //       .map(
            //         (shift) =>
            //             DropdownMenuItem(value: shift, child: Text(shift)),
            //       )
            //       .toList(),
            // ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text("Cancel"),
        ),
        if (submittingUser)
          const CircularProgressIndicator()
        else
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: logoTealColor,
              foregroundColor: whiteColor,
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 13),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: const TextStyle(fontWeight: FontWeight.bold),
            ),
            onPressed: () async {
              await onSubmit();
            },
            child: const Text("Save", style: TextStyle(color: whiteColor)),
          ),
      ],
    );
  }

  Future<void> onSubmit() async {
    // Prevent multiple submissions
    if (submittingUser) return;

    // Validate inputs before setting loading state
    if (nameCtrl.text.isEmpty) {
      showSnackBar("Please enter a name.");
      return;
    }

    if (emailCtrl.text.isEmpty ||
        !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(emailCtrl.text)) {
      showSnackBar("Please enter a valid email address.");
      return;
    }

    // if (phoneCtrl.text.length > 10) {
    //   showSnackBar("Invalid Phone Number");
    //   return;
    // }

    if (selectedRole == null) {
      showSnackBar("Please select a role.");
      return;
    }

    // Set loading state only after validation passes
    setState(() => submittingUser = true);

    final generatedPassword =
        "${nameCtrl.text.split(' ').first.toLowerCase()}${generateRandomId(4)}";

    try {
      final userData = {
        'role': selectedRole,
        'name': nameCtrl.text.trim(),
        'designation': designationCtrl.text.trim(),
        'email': emailCtrl.text.trim(),
        'password': generatedPassword,
        'phoneNo': phoneCtrl.text.trim(),
        'status': UserStatus.active,
        'customers': selectedCustomers,
        'sendEmail': true, // Enable email sending
      };

      // print("userData : $userData");

      final functionName = testMode ? 'testCreateUser' : 'createUser';

      final result = await FBFunctions.ff
          .httpsCallable(functionName)
          .call(userData);

      final resultData = Map<String, dynamic>.from(result.data);

      if (resultData['success'] != true || resultData['uid'] == null) {
        if (mounted) {
          if (resultData['code'] == 'user-already-exists') {
            showSnackBar("Email already exists!");
          } else {
            final errorMsg = resultData['msg'] ?? "Failed to create user.";
            showSnackBar("User creation failed: $errorMsg");
          }
          setState(() => submittingUser = false);
        }
        return;
      }

      if (mounted) {
        // Handle email sending result
        if (resultData['emailSent'] == true) {
          showSnackBar("User created successfully and welcome email sent!");
        } else {
          showSnackBar("User created successfully, but email sending failed.");
          if (resultData['emailError'] != null) {
            debugPrint("Email error: ${resultData['emailError']}");
          }
        }

        setState(() => submittingUser = false);
        context.pop(context);
      }
    } catch (e) {
      debugPrint(e.toString());
      if (mounted) {
        showSnackBar("An error occurred: ${e.toString()}");
        setState(() => submittingUser = false);
      }
    }
  }
}
