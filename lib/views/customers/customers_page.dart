import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:legacy_pms/common/common_searchbar.dart';
import 'package:legacy_pms/common/custom_header_button.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/shared/router.dart';
import 'package:legacy_pms/views/customers/add_customer.dart';
import 'package:legacy_pms/widgets/delete_confirmation_dialog.dart';
import 'package:path_provider/path_provider.dart';
import '../../controller/homectrl.dart';

// Brand Colors
const orangeColor = Color(0xFFEE8023);
const logoTealColor = Color(0xFF36748A);

class CustomersPage extends StatefulWidget {
  const CustomersPage({super.key});

  @override
  State<CustomersPage> createState() => _CustomersPageState();
}

class _CustomersPageState extends State<CustomersPage> {
  SearchController searchctrl = SearchController();
  bool customersExporting = false;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final query = searchctrl.text.toLowerCase();
        final loggedInUser = ctrl.loggedInUser;
        final assignedCustomerIds = loggedInUser?.customers ?? [];
        final isAdmin = loggedInUser?.role == admin;
        final filteredCustomers = query.isEmpty
            ? (isAdmin
                  ? ctrl.customers
                  : ctrl.customers
                        .where((c) => assignedCustomerIds.contains(c.docId))
                        .toList())
            : (isAdmin
                  ? ctrl.customers.where((customer) {
                      final nameMatch = customer.name.toLowerCase().contains(
                        query,
                      );
                      final companyMatch = customer.companyName
                          .toLowerCase()
                          .contains(query);
                      final emailMatch = (customer.email ?? '')
                          .toLowerCase()
                          .contains(query);
                      final phoneMatch = (customer.phoneNo ?? '')
                          .toLowerCase()
                          .contains(query);
                      return nameMatch ||
                          companyMatch ||
                          emailMatch ||
                          phoneMatch;
                    }).toList()
                  : ctrl.customers
                        .where((c) => assignedCustomerIds.contains(c.docId))
                        .where((customer) {
                          final nameMatch = customer.name
                              .toLowerCase()
                              .contains(query);
                          final companyMatch = customer.companyName
                              .toLowerCase()
                              .contains(query);
                          final emailMatch = (customer.email ?? '')
                              .toLowerCase()
                              .contains(query);
                          final phoneMatch = (customer.phoneNo ?? '')
                              .toLowerCase()
                              .contains(query);
                          return nameMatch ||
                              companyMatch ||
                              emailMatch ||
                              phoneMatch;
                        })
                        .toList());

        // Sort filtered customers by createdAt descending
        filteredCustomers.sort((a, b) => a.name.compareTo(b.name));

        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Add Customer Button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        "Customers (${filteredCustomers.length})",
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                          letterSpacing: 0.8,
                        ),
                      ),
                      SizedBox(width: 20),
                      CommonSearchBar(
                        searchController: searchctrl,
                        searchOnChanged: (p1) {
                          setState(() {});
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      customersExporting == true
                          ? CircularProgressIndicator()
                          : CustomHeaderButton(
                              onPressed: () async =>
                                  await exportCustomersToExcel(
                                    customers: filteredCustomers,
                                    context: context,
                                  ),
                              buttonName: "Export",
                            ),
                      const SizedBox(width: 10),
                      CustomHeaderButton(
                        onPressed:
                            ctrl.userRoles.contains(Permissions.canAddCustomer)
                            ? () => showDialog(
                                context: context,
                                builder: (context) => const AddCustomerDialog(),
                              )
                            : () {},
                        buttonName: "Add Customer",
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 24),

              /// Table Header
              Container(
                decoration: BoxDecoration(
                  color: logoTealColor.withOpacity(0.85),
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: const EdgeInsets.symmetric(
                  vertical: 14,
                  horizontal: 20,
                ),
                child: Row(
                  children: [
                    _headerCell("Sr. No", textTheme),
                    _headerCell("Customer Name", textTheme, flex: 2),
                    _headerCell("Company Name", textTheme, flex: 2),
                    _headerCell("Email", textTheme, flex: 2),
                    _headerCell("Phone", textTheme, flex: 2),
                    _headerCell("Timezone", textTheme, flex: 2),
                    _headerCell("Type", textTheme, flex: 2),
                    Expanded(
                      flex: 2,
                      child: Center(
                        child: Text(
                          "Actions",
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 8),

              /// Table Body
              filteredCustomers.isEmpty
                  ? Center(
                      heightFactor: 8,
                      child: Text(
                        "No Customers Available",
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    )
                  : Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                        side: BorderSide(color: logoTealColor.withOpacity(0.2)),
                      ),
                      child: Column(
                        children: List.generate(filteredCustomers.length, (
                          index,
                        ) {
                          final customer = filteredCustomers[index];
                          final isEven = index % 2 == 0;

                          return Column(
                            children: [
                              InkWell(
                                onTap:
                                    ctrl.userRoles.contains(
                                      Permissions.canSeeCustomersDetails,
                                    )
                                    ? () {
                                        context.push(
                                          '${Routes.customerdetails}/${customer.docId}',
                                        );
                                      }
                                    : null,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: isEven
                                        ? Colors.grey.shade50
                                        : Colors.white,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                    horizontal: 20,
                                  ),
                                  child: Row(
                                    children: [
                                      _bodyCell("${index + 1}", textTheme),
                                      _bodyCell(
                                        customer.name.capitalize.toString(),
                                        textTheme,
                                        flex: 2,
                                      ),
                                      _bodyCell(
                                        customer.companyName.capitalize
                                            .toString(),
                                        textTheme,
                                        flex: 2,
                                      ),
                                      _bodyCell(
                                        customer.email ?? '',
                                        textTheme,
                                        flex: 2,
                                      ),
                                      _bodyCell(
                                        customer.phoneNo ?? '',
                                        textTheme,
                                        flex: 2,
                                      ),

                                      Expanded(
                                        flex: 2,
                                        child: DropdownButton<String>(
                                          dropdownColor: Colors.white,
                                          value: customer.timeZone,
                                          items: CustomerTimezone.all
                                              .map(
                                                (tz) => DropdownMenuItem(
                                                  value: tz,
                                                  child: Text(
                                                    tz,
                                                    style: textTheme.bodyMedium,
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                          onChanged:
                                              ctrl.userRoles.contains(
                                                Permissions.canEditCustomer,
                                              )
                                              ? (newTz) async {
                                                  if (newTz != null) {
                                                    await ctrl
                                                        .updateCustomerField(
                                                          customer.docId,
                                                          'timeZone',
                                                          newTz,
                                                        );
                                                  }
                                                }
                                              : null,
                                          underline: SizedBox(),
                                          isExpanded: false,
                                        ),
                                      ),

                                      Expanded(
                                        flex: 2,
                                        child: DropdownButton<String>(
                                          dropdownColor: Colors.white,
                                          value: customer.clientType,
                                          items: CustomerType.all
                                              .map(
                                                (type) => DropdownMenuItem(
                                                  value: type,
                                                  child: Text(
                                                    type,
                                                    style: textTheme.bodyMedium,
                                                  ),
                                                ),
                                              )
                                              .toList(),
                                          onChanged:
                                              ctrl.userRoles.contains(
                                                Permissions.canEditCustomer,
                                              )
                                              ? (newType) async {
                                                  if (newType != null) {
                                                    await ctrl
                                                        .updateCustomerField(
                                                          customer.docId,
                                                          'clientType',
                                                          newType,
                                                        );
                                                  }
                                                }
                                              : null,
                                          underline: SizedBox(),
                                          isExpanded: false,
                                        ),
                                      ),

                                      CustomerActionButtons(
                                        customer: customer,
                                        ctrl: ctrl,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Divider(
                                height: 1,
                                thickness: 0.7,
                                color: logoTealColor.withOpacity(0.2),
                              ),
                            ],
                          );
                        }),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  Future<void> exportCustomersToExcel({
    required List<Customermodel> customers,
    required BuildContext context,
  }) async {
    try {
      setState(() {
        customersExporting = true;
      });
      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      final headers = [
        'Sr.No',
        'Customer Name',
        'Company Name',
        'Email',
        'Phone',
        'Timezone',
        'Type',
        // 'Created At',
      ];

      sheet.appendRow(headers.map((h) => TextCellValue(h)).toList());

      for (var customer in customers) {
        final row = [
          TextCellValue((customers.indexOf(customer) + 1).toString()),
          TextCellValue(customer.name),
          TextCellValue(customer.companyName),
          TextCellValue(customer.email ?? ''),
          TextCellValue(customer.phoneNo ?? ''),
          TextCellValue(customer.timeZone ?? ''),
          TextCellValue(customer.clientType ?? ''),
          // TextCellValue(
          //   customer.createdAt != null
          //       ? DateFormat('dd-MM-yyyy').format(customer.createdAt)
          //       : '',
          // ),
        ];
        sheet.appendRow(row);
      }

      final excelBytes = excel.encode();
      final filename =
          'Customers_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      if (kIsWeb) {
        await FileSaver.instance.saveFile(
          name: filename,
          bytes: Uint8List.fromList(excelBytes!),
          fileExtension: 'xlsx',
        );
        setState(() {
          customersExporting = false;
        });
      } else {
        final dir = await getExternalStorageDirectory();
        final filePath = '${dir!.path}/$filename';
        final file = File(filePath);
        await file.writeAsBytes(excelBytes!);
        setState(() {
          customersExporting = false;
        });
        showCtcAppSnackBar(context, 'Exported customers to $filePath');
      }
    } catch (e) {
      showCtcAppSnackBar(context, 'Failed to export customers: $e');
      debugPrint(e.toString());
      setState(() {
        customersExporting = false;
      });
    }
  }

  /// --- Helper widgets ---
  Widget _headerCell(String text, TextTheme theme, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: theme.titleMedium?.copyWith(
          fontWeight: FontWeight.w700,
          color: Colors.white,
          letterSpacing: 0.8,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _bodyCell(
    String text,
    TextTheme theme, {
    int flex = 1,
    Color? textColor,
  }) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: theme.bodyMedium?.copyWith(
          overflow: TextOverflow.clip,
          fontSize: 14,
          color: textColor ?? Colors.black87,
        ),
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

class CustomerActionButtons extends StatelessWidget {
  const CustomerActionButtons({
    super.key,
    required this.customer,
    required this.ctrl,
  });

  final Customermodel customer;
  final HomeCtrl ctrl;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 2,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Transform.scale(
            scale: 0.8,
            child: CupertinoSwitch(
              inactiveTrackColor: Colors.grey.shade400,
              value: customer.status == "active",
              onChanged: ctrl.userRoles.contains(Permissions.canEditCustomer)
                  ? (value) {
                      ctrl.updateCustomerStatus(
                        customer.docId,
                        value ? 'active' : 'inactive',
                      );
                    }
                  : null,
            ),
          ),
          if (ctrl.userRoles.contains(Permissions.canDeleteCustomer))
            IconButton(
              icon: const Icon(Icons.delete),
              color: Colors.redAccent,
              tooltip: "Delete Customer",
              onPressed: () async {
                await showDialog<bool>(
                  context: context,
                  builder: (context) => DeleteConfirmationDialog(
                    title: "Confirm Delete",
                    content: "Are you sure you want to delete this customer?",
                    onDelete: () => ctrl.deleteCustomer(customer.docId),
                    successMessage: "Customer deleted successfully",
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}
