import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:legacy_pms/models/mastertaskmodel.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';
import 'package:legacy_pms/shared/const.dart'; // For colors like greenColor and whiteColor if needed

class AddCustomerDialog extends StatefulWidget {
  const AddCustomerDialog({super.key});

  @override
  State<AddCustomerDialog> createState() => _AddCustomerDialogState();
}

class _AddCustomerDialogState extends State<AddCustomerDialog> {
  final nameCtrl = TextEditingController();
  final phonenoCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  final companynameCtrl = TextEditingController();
  String? selectedClientType;
  String? selectedClientTimezone;
  List<String> selectedMasterTasks = [];
  bool submittingCustomer = false;
  List<Mastertaskmodel> availableMasterTasks = [];

  Future<List<Map<String, dynamic>>> fetchMasterTasks() async {
    final snapshot = await FBFireStore.masterTasks.get();
    return snapshot.docs
        .map((doc) => {'id': doc.id, 'name': doc['title'] ?? 'No Name'})
        .toList();
  }

  Widget _buildMasterTasksDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Master Tasks (Optional)",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              // Selected Tasks Display
              if (selectedMasterTasks.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: selectedMasterTasks.map((taskId) {
                    final task = availableMasterTasks.firstWhere(
                      (t) => t.docId == taskId,
                      orElse: () => Mastertaskmodel(
                        docId: taskId,
                        title: 'Unknown Task',
                        desc: '',
                        createdAt: DateTime.now(),
                        type: '',
                        benchmark: '',
                        showCount: false,
                        masterQc: '',
                      ),
                    );
                    return Chip(
                      label: Text(
                        task.title,
                        style: const TextStyle(fontSize: 12),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          selectedMasterTasks.remove(taskId);
                        });
                      },
                      backgroundColor: logoTealColor.withOpacity(0.1),
                      deleteIconColor: logoTealColor,
                      labelStyle: TextStyle(color: logoTealColor),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 12),
              ],
              // Dropdown for adding tasks
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: "Add Master Task",
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.add),
                ),
                value: null, // Always null to show placeholder
                items: availableMasterTasks
                    .where((task) => !selectedMasterTasks.contains(task.docId))
                    .map(
                      (task) => DropdownMenuItem(
                        value: task.docId,
                        child: Text(task.title),
                      ),
                    )
                    .toList(),
                onChanged: (String? taskId) {
                  if (taskId != null) {
                    setState(() {
                      selectedMasterTasks.add(taskId);
                    });
                  }
                },
                hint: const Text('Select a master task to add'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: popupBgColor,
      title: const Text("Add Customer"),
      insetPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Name TextField
            TextField(
              controller: nameCtrl,
              decoration: const InputDecoration(
                labelText: "Name",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: companynameCtrl,
              decoration: const InputDecoration(
                labelText: "Company Name",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: emailCtrl,
              decoration: const InputDecoration(
                labelText: "Email",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: phonenoCtrl,
              decoration: const InputDecoration(
                labelText: "Phone No",
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            // Client Type Dropdown
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: "Client Type",
                border: OutlineInputBorder(),
              ),
              value: selectedClientType,
              onChanged: (val) => setState(() => selectedClientType = val),
              items: CustomerType.all
                  .map(
                    (type) => DropdownMenuItem(value: type, child: Text(type)),
                  )
                  .toList(),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              decoration: const InputDecoration(
                labelText: "Client Timezone",
                border: OutlineInputBorder(),
              ),
              value: selectedClientTimezone,
              onChanged: (val) => setState(() => selectedClientTimezone = val),
              items: CustomerTimezone.all
                  .map(
                    (type) => DropdownMenuItem(value: type, child: Text(type)),
                  )
                  .toList(),
            ),
            const SizedBox(height: 12),
            // Master Tasks Multi-Select Dropdown
            FutureBuilder<List<Mastertaskmodel>>(
              future: FBFireStore.masterTasks.get().then(
                (snapshot) => snapshot.docs
                    .map((doc) => Mastertaskmodel.fromSnap(doc))
                    .toList(),
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Text('No Master Tasks available');
                }

                availableMasterTasks = snapshot.data!;
                return _buildMasterTasksDropdown();
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text("Cancel"),
        ),
        if (submittingCustomer) ...[
          const CircularProgressIndicator(),
        ] else ...[
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: logoTealColor,
              foregroundColor: whiteColor,
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 13),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              textStyle: const TextStyle(fontWeight: FontWeight.bold),
            ),
            onPressed: () async {
              await onSubmit();
            },
            child: const Text("Save", style: TextStyle(color: whiteColor)),
          ),
        ],
      ],
    );
  }

  Future<void> onSubmit() async {
    // Prevent multiple submissions
    if (submittingCustomer) return;

    // Validate inputs before setting loading state
    if (nameCtrl.text.trim().isEmpty) {
      showSnackBar("Please enter customer's name.");
      return;
    }
    if (companynameCtrl.text.trim().isEmpty) {
      showSnackBar("Please enter company name.");
      return;
    }

    if (emailCtrl.text.isNotEmpty &&
        !RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(emailCtrl.text)) {
      showSnackBar("Please enter a valid email address.");
      return;
    }

    // if (phonenoCtrl.text.isNotEmpty && (phonenoCtrl.text.length != 10)) {
    //   showSnackBar("Invalid Phone Number");
    //   return;
    // }

    if (selectedClientType == null) {
      showSnackBar("Please select a client type.");
      return;
    }

    if (selectedClientTimezone == null) {
      showSnackBar("Please select a client timezone.");
      return;
    }

    // Set loading state only after validation passes
    setState(() => submittingCustomer = true);

    try {
      final newCustomer = {
        'name': nameCtrl.text.trim(),
        'clientType': selectedClientType,
        'timeZone': selectedClientTimezone,
        'createdAt': FieldValue.serverTimestamp(),
        'companyName': companynameCtrl.text.trim(),
        'email': emailCtrl.text.trim(),
        'phoneNo': phonenoCtrl.text.trim(),
        'status': null,
        'masterTasks': selectedMasterTasks,
      };

      await FBFireStore.customers.add(newCustomer);

      if (mounted) {
        showSnackBar("Customer added successfully.");
        setState(() => submittingCustomer = false);
        Navigator.of(context).pop(true); // Indicate success
      }
    } catch (e) {
      if (mounted) {
        showSnackBar("Failed to add customer: $e");
        setState(() => submittingCustomer = false);
      }
    }
  }

  @override
  void dispose() {
    nameCtrl.dispose();
    super.dispose();
  }
}
