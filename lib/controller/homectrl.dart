import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:legacy_pms/models/customermodel.dart';
import 'package:legacy_pms/models/masterqcmodel.dart';
import 'package:legacy_pms/models/mastertaskmodel.dart';
import 'package:legacy_pms/models/punchmodel.dart';
import 'package:legacy_pms/models/recordsmodel.dart';
import 'package:legacy_pms/models/requestmodel.dart';
import 'package:legacy_pms/models/rolemodel.dart';
import 'package:legacy_pms/models/settingsmodel.dart';
import 'package:legacy_pms/models/usermodel.dart';
import 'package:legacy_pms/models/taskmodel.dart';
import 'package:legacy_pms/shared/const.dart';
import 'package:legacy_pms/shared/firebase.dart';
import 'package:legacy_pms/shared/methods.dart';

class HomeCtrl extends GetxController {
  List<Usermodel> users = [];
  List<Customermodel> customers = [];
  List<TaskModel> tasks = [];
  List<Rolemodel> roles = [];
  List<Mastertaskmodel> masterTasks = [];
  List<Masterqcmodel> masterQcs = [];
  Punchmodel? userlatestPunch;
  bool todaysPunch = true;
  List<String> userRoles = [];
  List<UserTodaydata> userTodaydata = [];
  List<Requestmodel> requestsPunchOut = [];
  Usermodel? loggedInUser;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? usersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? customersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? tasksStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? rolesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? masterTasksStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? punchesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? masterQcsStream;
  StreamSubscription<User?>? authStateStream; // <-- Added auth state stream
  bool attendeeLoading = true;

  bool puchLoading = true;

  Settingsmodel? settings;

  @override
  void onInit() {
    super.onInit();
    getusersdata();
    getcustomerdata();
    gettasksdata();
    getrolesdata();
    getmastertasksdata();
    getmasterQcsdata();
    getSettinngs();
    getRequestsPunchOut();

    // Listen to auth state changes
    authStateStream?.cancel();
    authStateStream = FirebaseAuth.instance.authStateChanges().listen((user) {
      // You can add logic here to react to auth state changes
      // For example, update loggedInUser or clear data on logout
      if (user == null) {
        loggedInUser = null;
        userRoles = [];
        update();
      } else {
        getLoggedInUser();
        getLoggedInUserPermissions();
        update();
      }
    });
  }

  @override
  void onClose() {
    usersStream?.cancel();
    customersStream?.cancel();
    tasksStream?.cancel();
    rolesStream?.cancel();
    masterTasksStream?.cancel();
    authStateStream?.cancel(); // Cancel auth state stream
    super.onClose();
  }

  Future<void> getLoggedInUser() async {
    final authUser = FirebaseAuth.instance.currentUser;

    final user = users.firstWhereOrNull((user) {
      return user.email == authUser?.email;
    });

    loggedInUser = user;
  }

  Future<void> getLoggedInUserPermissions() async {
    if (loggedInUser == null) return;

    final role = roles.firstWhereOrNull((r) {
      return r.title.toLowerCase() == loggedInUser?.role.toLowerCase();
    });

    userRoles = role?.permissions ?? [];
  }

  Future<void> getusersdata() async {
    try {
      usersStream?.cancel();
      usersStream = FBFireStore.users.snapshots().listen((event) {
        users = event.docs.map((e) => Usermodel.fromSnap(e)).toList();
        getLoggedInUser();

        getUserLastPunch();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getcustomerdata() async {
    try {
      customersStream?.cancel();
      customersStream = FBFireStore.customers.snapshots().listen((event) {
        customers = event.docs.map((e) => Customermodel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> gettasksdata() async {
    try {
      tasksStream?.cancel();
      tasksStream = FBFireStore.tasks.snapshots().listen((event) {
        tasks = event.docs.map((e) => TaskModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getmastertasksdata() async {
    try {
      masterTasksStream?.cancel();
      masterTasksStream = FBFireStore.masterTasks.snapshots().listen((event) {
        masterTasks = event.docs
            .map((e) => Mastertaskmodel.fromSnap(e))
            .toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getmasterQcsdata() async {
    try {
      masterQcsStream?.cancel();
      masterQcsStream = FBFireStore.masterQc.snapshots().listen((event) {
        masterQcs = event.docs.map((e) => Masterqcmodel.fromSnap(e)).toList();
        update();
        debugPrint("masterQcs: ${masterQcs.length}");
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getrolesdata() async {
    try {
      rolesStream?.cancel();
      rolesStream = FBFireStore.roles.snapshots().listen((event) {
        roles = event.docs.map((e) => Rolemodel.fromSnap(e)).toList();
        getLoggedInUserPermissions();

        if (userRoles.contains(Permissions.canSeeAttendanceDashboard)) {
          getUserTodayData();
        }
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> updateUserStatus(String docId, String newStatus) async {
    try {
      await FBFireStore.users.doc(docId).update({'status': newStatus});
      final index = users.indexWhere((user) => user.docId == docId);
      if (index != -1) {
        users[index] = users[index].copyWith(status: newStatus);
        update();
      }
    } catch (e) {
      debugPrint("Error updating user status: $e");
    }
  }

  Future<void> deleteUser(String docId) async {
    try {
      // Call the cloud function to delete the user from Firebase Auth
      final result = await FBFunctions.ff.httpsCallable('deleteUser').call({
        'uid': docId,
      });
      final resultData = Map<String, dynamic>.from(result.data);

      if (resultData['success'] == true) {
        // If cloud function succeeded, remove from Firestore and local list
        await FBFireStore.users.doc(docId).delete();
        users.removeWhere((user) => user.docId == docId);
        update();
      } else {
        final errorMsg = resultData['msg'] ?? 'Unknown error occurred';
        debugPrint("Cloud function error: $errorMsg");
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint("Error deleting user: $e");
      rethrow; // Re-throw to let the UI handle the error
    }
  }

  Future<void> updateCustomerStatus(String docId, String newStatus) async {
    try {
      await FBFireStore.customers.doc(docId).update({'status': newStatus});
      final index = customers.indexWhere((c) => c.docId == docId);
      if (index != -1) {
        customers[index] = customers[index].copyWith(status: newStatus);
        update();
      }
    } catch (e) {
      debugPrint("Error updating customer status: $e");
    }
  }

  Future<void> deleteCustomer(String docId) async {
    try {
      await FBFireStore.customers.doc(docId).delete();
      customers.removeWhere((c) => c.docId == docId);
      update();
    } catch (e) {
      debugPrint("Error deleting customer: $e");
      rethrow; // Re-throw to let the UI handle the error
    }
  }

  Future<void> updateCustomerField(
    String docId,
    String field,
    dynamic value,
  ) async {
    await FBFireStore.customers.doc(docId).update({field: value});
  }

  Future<void> getUserLastPunch() async {
    try {
      punchesStream?.cancel();
      punchesStream = FBFireStore.punches
          .where('uId', isEqualTo: loggedInUser?.docId)
          .orderBy('createdAt', descending: true)
          .limit(1)
          .snapshots()
          .listen((event) async {
            if (event.docs.isEmpty) {
              userlatestPunch = null;
              puchLoading = false;

              if ((loggedInUser?.role ?? "HR , Admin") != 'HR , Admin') {
                await punchIn(this);
              }

              update();
              return;
            }
            userlatestPunch = Punchmodel.fromSnap(event.docs.first);
            final now = DateTime.now();
            if (userlatestPunch == null) {
              todaysPunch = false;
              puchLoading = false;
              update();
              return;
            }
            final punchDate = userlatestPunch!.createdAt;

            if (punchDate.year == now.year &&
                punchDate.month == now.month &&
                punchDate.day == now.day) {
              todaysPunch = true;
            } else {
              todaysPunch = false;
            }

            if ((!todaysPunch || !userlatestPunch!.punchIn) &&
                (loggedInUser?.role ?? "HR , Admin") != 'HR , Admin') {
              await punchIn(this);
            }

            puchLoading = false;
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
      puchLoading = false;
    }
  }

  Future<void> getSettinngs() async {
    try {
      final sets = await FBFireStore.settings.get();
      if (sets.exists) {
        settings = Settingsmodel.fromSnap(sets);
        update();
        return;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getRequestsPunchOut() async {
    try {
      FBFireStore.requestPunchOut
          .where('active', isEqualTo: true)
          .snapshots()
          .listen((event) {
            requestsPunchOut = event.docs
                .map((e) => Requestmodel.fromSnap(e))
                .toList();

            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> getUserTodayData() async {
    try {
      userTodaydata.clear();
      attendeeLoading = true;
      update();

      // Loop through all users
      for (final user in users) {
        final userId = user.docId;

        // Get today's start and end
        final now = DateTime.now();
        final todayStart = DateTime(
          now.year,
          now.month,
          now.day,
          0,
          0,
          0,
          0,
          0,
        );
        final todayEnd = DateTime(
          now.year,
          now.month,
          now.day,
          23,
          59,
          59,
          999,
          999,
        );

        // Get punches for today for this user
        final punchesQuery = await FBFireStore.punches
            .where('uId', isEqualTo: userId)
            .where('createdAt', isGreaterThanOrEqualTo: todayStart)
            .where('createdAt', isLessThanOrEqualTo: todayEnd)
            .get();

        final punches = punchesQuery.docs
            .map((e) => Punchmodel.fromSnap(e))
            .toList();

        // Get latest record for this user
        final recordQuery = await FBFireStore.records
            .where('uId', isEqualTo: userId)
            .orderBy('createdAt', descending: true)
            .limit(1)
            .get();

        final record = recordQuery.docs.isNotEmpty
            ? Recordsmodel.fromSnap(recordQuery.docs.first)
            : null;

        userTodaydata.add(
          UserTodaydata(
            userTodayPunches: punches,
            userRecord: record,
            user: user,
          ),
        );
      }
      attendeeLoading = false;
      update();
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

class UserTodaydata {
  List<Punchmodel> userTodayPunches;
  Recordsmodel? userRecord;
  Usermodel user;

  UserTodaydata({
    required this.userTodayPunches,
    required this.userRecord,
    required this.user,
  });
}
