import 'package:flutter/material.dart';
import 'package:legacy_pms/shared/const.dart';

class DeleteConfirmationDialog extends StatefulWidget {
  final String title;
  final String content;
  final Future<void> Function() onDelete;
  final String? successMessage;

  const DeleteConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    required this.onDelete,
    this.successMessage,
  });

  @override
  State<DeleteConfirmationDialog> createState() => _DeleteConfirmationDialogState();
}

class _DeleteConfirmationDialogState extends State<DeleteConfirmationDialog> {
  bool isDeleting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: popupBgColor,
      title: Text(widget.title),
      content: Text(widget.content),
      actions: [
        TextButton(
          onPressed: isDeleting ? null : () => Navigator.of(context).pop(false),
          child: const Text("Cancel"),
        ),
        ElevatedButton(
          onPressed: isDeleting ? null : () async {
            setState(() => isDeleting = true);
            
            try {
              await widget.onDelete();
              if (mounted) {
                Navigator.of(context).pop(true);
                if (widget.successMessage != null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(widget.successMessage!)),
                  );
                }
              }
            } catch (e) {
              if (mounted) {
                setState(() => isDeleting = false);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("Delete failed: $e"),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: isDeleting
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text("Delete"),
        ),
      ],
    );
  }
}
